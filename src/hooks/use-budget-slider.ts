"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { useReducedMotion } from "framer-motion";

// Budget slider configuration
export const BUDGET_CONFIG = {
    MIN: 300,
    MAX: 10000,
    STEP: 100,
    DEFAULT: 1000,
} as const;

// Currency formatting utility
export const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(value);
};

interface UseBudgetSliderOptions {
    initialValue?: number;
    onValueChange?: (value: number) => void;
    debounceMs?: number;
}

interface UseBudgetSliderReturn {
    value: number;
    formattedValue: string;
    isInteracting: boolean;
    handleValueChange: (value: number[]) => void;
    handleInteractionStart: () => void;
    handleInteractionEnd: () => void;
    setValue: (value: number) => void;
}

/**
 * Enhanced hook for budget slider management with smooth interactions
 * Provides debounced updates, interaction states, and accessibility features
 */
export function useBudgetSlider({
    initialValue = BUDGET_CONFIG.DEFAULT,
    onValueChange,
    debounceMs = 150,
}: UseBudgetSliderOptions = {}): UseBudgetSliderReturn {
    const [value, setValue] = useState(initialValue);
    const [isInteracting, setIsInteracting] = useState(false);
    const shouldReduceMotion = useReducedMotion();
    
    // Refs for debouncing and cleanup
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const lastValueRef = useRef(value);

    // Memoized formatted value to prevent unnecessary recalculations
    const formattedValue = formatCurrency(value);

    /**
     * Handles slider value changes with debouncing for performance
     * Immediately updates local state but debounces external callbacks
     */
    const handleValueChange = useCallback((newValue: number[]) => {
        const numericValue = newValue[0];
        setValue(numericValue);
        lastValueRef.current = numericValue;

        // Clear existing timeout
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }

        // Debounce external callback unless user prefers reduced motion
        const delay = shouldReduceMotion ? 0 : debounceMs;
        
        debounceTimeoutRef.current = setTimeout(() => {
            onValueChange?.(numericValue);
        }, delay);
    }, [onValueChange, debounceMs, shouldReduceMotion]);

    /**
     * Handles interaction start (mouse down, touch start)
     * Provides immediate feedback for better UX
     */
    const handleInteractionStart = useCallback(() => {
        setIsInteracting(true);
        
        // Clear any pending debounced calls for immediate responsiveness
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
            debounceTimeoutRef.current = null;
        }
    }, []);

    /**
     * Handles interaction end (mouse up, touch end)
     * Ensures final value is properly committed
     */
    const handleInteractionEnd = useCallback(() => {
        setIsInteracting(false);
        
        // Ensure final value is committed immediately
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }
        onValueChange?.(lastValueRef.current);
    }, [onValueChange]);

    /**
     * Programmatically set slider value
     * Useful for external updates or reset functionality
     */
    const setSliderValue = useCallback((newValue: number) => {
        const clampedValue = Math.max(
            BUDGET_CONFIG.MIN,
            Math.min(BUDGET_CONFIG.MAX, newValue)
        );
        setValue(clampedValue);
        lastValueRef.current = clampedValue;
        onValueChange?.(clampedValue);
    }, [onValueChange]);

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
        };
    }, []);

    // Sync with external value changes
    useEffect(() => {
        if (initialValue !== value && !isInteracting) {
            setValue(initialValue);
            lastValueRef.current = initialValue;
        }
    }, [initialValue, value, isInteracting]);

    return {
        value,
        formattedValue,
        isInteracting,
        handleValueChange,
        handleInteractionStart,
        handleInteractionEnd,
        setValue: setSliderValue,
    };
}
